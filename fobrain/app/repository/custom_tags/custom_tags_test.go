package custom_tags

import (
	"encoding/json"
	"net/http"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"

	req "fobrain/fobrain/app/request/custom_tags"
	testcommon "fobrain/fobrain/tests/common_test"
)

func TestCreate(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	t.Run("Create Error", func(t *testing.T) {
		tagName := "自定义标签"
		mockDb.ExpectQuery("SELECT count(*) FROM `custom_tags` WHERE tag_name = ?").
			WithArgs(tagName).
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))
		params := req.InsertCustomTagsRequest{
			TagName: tagName,
		}
		err := Create(&params)
		assert.Equal(t, "标签已经存在，无法继续添加", err.Error())
		// 确保数据库期望被调用
		assert.NoError(t, mockDb.ExpectationsWereMet())
	})
	t.Run("Create", func(t *testing.T) {
		tagName := "tag标签"
		mockDb.ExpectQuery("SELECT count(*) FROM `custom_tags` WHERE tag_name = ?").
			WithArgs(tagName).
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(0))

		mockDb.Mock.ExpectBegin()
		// 设置插入操作，模拟标签插入
		mockDb.ExpectExec("INSERT INTO `custom_tags` (`created_at`,`updated_at`,`tag_name`) VALUES (?,?,?)").
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), tagName).
			WillReturnResult(sqlmock.NewResult(1, 1)) // 假设插入成功，返回一行受影响数据
		mockDb.Mock.ExpectCommit()

		params := req.InsertCustomTagsRequest{
			TagName: tagName,
		}
		err := Create(&params)
		assert.NoError(t, err)
		assert.NoError(t, mockDb.ExpectationsWereMet())
	})
}

func TestALlList(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT count(*) FROM `custom_tags`").
		WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

	mockDb.ExpectQuery("SELECT * FROM `custom_tags`").
		WillReturnRows(mockDb.NewRows([]string{"id"}).AddRow(1))

	params := req.SearchCustomTagsRequest{
		Keyword: "",
	}
	list, total, err := ALlList(&params)
	assert.NoError(t, err)
	assert.Equal(t, int64(1), total)
	assert.Equal(t, 1, len(list))

	// 确保数据库期望的查询都被执行
	assert.NoError(t, mockDb.ExpectationsWereMet())
}

func TestDelete(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// 创建ES Mock服务器
	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	t.Run("Delete Success", func(t *testing.T) {
		tagId := "1"
		tagName := "测试标签"

		// Mock FindById 查询
		mockDb.ExpectQuery("SELECT * FROM `custom_tags` WHERE id = ? ORDER BY `custom_tags`.`id` LIMIT 1").
			WithArgs(tagId).
			WillReturnRows(sqlmock.NewRows([]string{"id", "tag_name"}).AddRow(1, tagName))

		// Mock ES update_by_query 响应
		mockServer.RegisterHandler("/assets/_update_by_query", func(w http.ResponseWriter, r *http.Request) {
			w.Header().Set("Content-Type", "application/json")
			response := map[string]interface{}{
				"took":              100,
				"timed_out":         false,
				"total":             2,
				"updated":           2,
				"deleted":           0,
				"batches":           1,
				"version_conflicts": 0,
				"noops":             0,
				"retries": map[string]int{
					"bulk":   0,
					"search": 0,
				},
				"throttled_millis":       0,
				"requests_per_second":    -1.0,
				"throttled_until_millis": 0,
				"failures":               []interface{}{},
			}
			json.NewEncoder(w).Encode(response)
		})

		// Mock DeleteById 操作
		mockDb.Mock.ExpectBegin()
		mockDb.ExpectExec("DELETE FROM `custom_tags` WHERE `custom_tags`.`id` = ?").
			WithArgs(tagId).
			WillReturnResult(sqlmock.NewResult(0, 1))
		mockDb.Mock.ExpectCommit()

		err := Delete(tagId)
		assert.NoError(t, err)
		assert.NoError(t, mockDb.ExpectationsWereMet())
	})
}
