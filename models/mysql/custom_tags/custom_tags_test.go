package custom_tags

import (
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"

	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/initialize/mysql"
)

func TestCount(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	model := NewCustomTagsConfig()
	mockDb.ExpectQuery("SELECT count(*) FROM `custom_tags`").
		WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

	var handlers []mysql.HandleFunc
	total, err := model.Count(handlers...)
	assert.NoError(t, err)
	assert.Equal(t, int64(1), total)
}

func TestCreate(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	mockDb.Mock.ExpectBegin()
	mockDb.ExpectExec("INSERT INTO `custom_tags` (`created_at`,`updated_at`,`tag_name`) VALUES (?,?,?)").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.Mock.ExpectCommit()

	model := NewCustomTagsConfig()
	err := model.CreateItem(&CustomTags{TagName: "test"})
	assert.NoError(t, err)
}

func TestList(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	model := NewCustomTagsConfig()

	mockDb.ExpectQuery("SELECT count(*) FROM `custom_tags`").
		WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

	mockDb.ExpectQuery("SELECT * FROM `custom_tags` LIMIT 10").
		WillReturnRows(mockDb.NewRows([]string{"id"}).AddRow(1))

	var handlers []mysql.HandleFunc
	list, count, err := model.List(1, 10, handlers...)
	assert.Nil(t, err)
	assert.Equal(t, int64(1), count)
	assert.Equal(t, 1, len(list))
}

func TestFindById(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	model := NewCustomTagsConfig()

	tagId := "1"
	tagName := "测试标签"

	mockDb.ExpectQuery("SELECT * FROM `custom_tags` WHERE id = ? ORDER BY `custom_tags`.`id` LIMIT 1").
		WithArgs(tagId).
		WillReturnRows(sqlmock.NewRows([]string{"id", "tag_name"}).AddRow(1, tagName))

	tag, err := model.FindById(tagId)
	assert.NoError(t, err)
	assert.NotNil(t, tag)
	assert.Equal(t, tagName, tag.TagName)
	assert.NoError(t, mockDb.ExpectationsWereMet())
}

func TestDeleteById(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	model := NewCustomTagsConfig()

	tagId := "1"

	mockDb.Mock.ExpectBegin()
	mockDb.ExpectExec("DELETE FROM `custom_tags` WHERE `custom_tags`.`id` = ?").
		WithArgs(tagId).
		WillReturnResult(sqlmock.NewResult(0, 1))
	mockDb.Mock.ExpectCommit()

	err := model.DeleteById(tagId)
	assert.NoError(t, err)
	assert.NoError(t, mockDb.ExpectationsWereMet())
}
