package data_board

import "fobrain/models/mysql/system_configs"

// 所有静态的看板配置结构体

var AssetsConfigList = []*DataBoardConfigList{
	{
		Title:         "全局视角",
		Key:           "global_perspective",
		Type:          "",
		FlagCondition: &SearchCondition{SearchField: "", Options: []*Options{}},
		Children: []*DataBoardConfigList{
			{
				Title:         "内外网资产占比",
				Key:           "network_assets_proportion",
				Type:          "pie",
				FlagCondition: &SearchCondition{SearchField: "", Options: []*Options{}},
			},
			{
				Title:         "无主资产统计",
				Key:           "ownerless_assets_statistics",
				Type:          "bar",
				FlagCondition: &SearchCondition{SearchField: "", Options: []*Options{}},
			},
			{
				Title:         "无主资产占比",
				Key:           system_configs.OwnerlessAssetsProportion,
				Type:          "pie",
				FlagCondition: &SearchCondition{SearchField: "business", Options: []*Options{{Label: "业务系统负责人", Value: "business"}, {Label: "运维人员", Value: "oper"}}},
			},
			{
				Title:         "业务系统状态分布",
				Key:           "business_status_distribution",
				Type:          "bar",
				FlagCondition: &SearchCondition{SearchField: "", Options: []*Options{}},
			},
			{
				Title:         "人员管理资产TOP5",
				Key:           "personnel_assets_top5",
				Type:          "bar",
				FlagCondition: &SearchCondition{SearchField: "oper", Options: []*Options{{Label: "业务系统负责人", Value: "business"}, {Label: "运维人员", Value: "oper"}}},
			},
			{
				Title:         "业务系统关联资产统计",
				Key:           system_configs.BusinessAssetStatistics,
				Type:          "bar",
				FlagCondition: &SearchCondition{SearchField: "", Options: []*Options{}},
			},
			{
				Title:         "资产融合统计",
				Key:           "asset_integration_statistics",
				Type:          "table",
				FlagCondition: &SearchCondition{SearchField: "", Options: []*Options{}},
			},
			{
				Title:         "探针资产数据贡献度",
				Key:           system_configs.ProbeAssetContributionDegree,
				Type:          "bar",
				FlagCondition: &SearchCondition{SearchField: "", Options: []*Options{}},
			},
			{
				Title:         "资产组件占比TOP10",
				Key:           system_configs.AssetComponentProportion,
				Type:          "bar",
				FlagCondition: &SearchCondition{SearchField: "", Options: []*Options{}},
			},
			{
				Title:         "近一周资产变化",
				Key:           "week_asset_count",
				Type:          "line",
				FlagCondition: &SearchCondition{SearchField: "", Options: []*Options{}},
			},
		},
	},
	{
		Title:         "部门视角",
		Key:           "department_perspective",
		Type:          "",
		FlagCondition: &SearchCondition{SearchField: "", Options: []*Options{}},
		Children: []*DataBoardConfigList{
			{
				Title:         "各部门资产数量",
				Key:           "department_asset_num",
				Type:          "bar",
				FlagCondition: &SearchCondition{SearchField: "2", Options: []*Options{{Label: "一级部门", Value: "1"}, {Label: "二级部门", Value: "2"}, {Label: "三级部门", Value: "3"}}},
			},
			{
				Title:         "各部门业务系统数量",
				Key:           "department_business_system_num",
				Type:          "bar",
				FlagCondition: &SearchCondition{SearchField: "1", Options: []*Options{{Label: "一级部门", Value: "1"}, {Label: "二级部门", Value: "2"}, {Label: "三级部门", Value: "3"}}},
			},
		},
	},
}

var VulConfigList = []*DataBoardConfigList{
	{
		Title:         "全局视角",
		Key:           "global_perspective",
		Type:          "",
		FlagCondition: &SearchCondition{SearchField: "", Options: []*Options{}},
		Children: []*DataBoardConfigList{
			{
				Title:         "内外网漏洞占比",
				Key:           "network_vul_proportion",
				Type:          "pie",
				FlagCondition: &SearchCondition{SearchField: "", Options: []*Options{}},
			},
			{
				Title:         "漏洞风险等级分布",
				Key:           "vul_risk_level",
				Type:          "pie",
				FlagCondition: &SearchCondition{SearchField: "all", Options: []*Options{{Label: "全部", Value: "all"}, {Label: "严重", Value: "4"}, {Label: "高危", Value: "3"}, {Label: "中危", Value: "2"}, {Label: "低危", Value: "1"}, {Label: "未知", Value: "5"}}},
			},
			{
				Title:         "漏洞修复优先级分布",
				Key:           "vul_fix_priority",
				Type:          "table",
				FlagCondition: &SearchCondition{SearchField: "", Options: []*Options{}},
			},
			{
				Title:         "漏洞修复状态分布",
				Key:           "vul_fix_status",
				Type:          "bar",
				FlagCondition: &SearchCondition{SearchField: "", Options: []*Options{}},
			},
			{
				Title:         "未整改漏洞占比情况",
				Key:           "unremediated_vul_ratio",
				Type:          "line",
				FlagCondition: &SearchCondition{SearchField: "month", Options: []*Options{{Label: "年度", Value: "year"}, {Label: "季度", Value: "quarter"}, {Label: "月度", Value: "month"}}},
			},
			{
				Title:         "漏洞类型数量TOP5",
				Key:           "vul_type_top5",
				Type:          "bar",
				FlagCondition: &SearchCondition{SearchField: "", Options: []*Options{}},
			},
			{
				Title:         "POC漏洞修复情况",
				Key:           "vul_poc_repaired_info",
				Type:          "pie",
				FlagCondition: &SearchCondition{SearchField: "", Options: []*Options{}},
			},
			{
				Title:         "IP关联漏洞TOP10",
				Key:           "ip_vul_top10",
				Type:          "bar",
				FlagCondition: &SearchCondition{SearchField: "", Options: []*Options{}},
			},
			{
				Title:         "互联网漏洞暴露TOP10",
				Key:           system_configs.InternetVulnerabilityTop10,
				Type:          "bar",
				FlagCondition: &SearchCondition{SearchField: "", Options: []*Options{}},
			},
			{
				Title:         "业务系统漏洞TOP5",
				Key:           "business_vul_top5",
				Type:          "bar",
				FlagCondition: &SearchCondition{SearchField: "", Options: []*Options{}},
			},
		},
	},
	{
		Title:         "部门视角",
		Key:           "department_perspective",
		Type:          "",
		FlagCondition: &SearchCondition{SearchField: "", Options: []*Options{}},
		Children: []*DataBoardConfigList{
			{
				Title:         "部门维度漏洞数量统计",
				Key:           "department_vul_statistics",
				Type:          "bar",
				FlagCondition: &SearchCondition{SearchField: "2", Options: []*Options{{Label: "一级部门", Value: "1"}, {Label: "二级部门", Value: "2"}, {Label: "三级部门", Value: "3"}}},
			},
			{
				Title:         "部门漏洞TOP5",
				Key:           "department_vul_top5",
				Type:          "bar",
				FlagCondition: &SearchCondition{SearchField: "1", Options: []*Options{{Label: "一级部门", Value: "1"}, {Label: "二级部门", Value: "2"}, {Label: "三级部门", Value: "3"}}},
			},
			{
				Title:         "未整改漏洞部门分布",
				Key:           "department_nofixed_vul_statistics",
				Type:          "bar",
				FlagCondition: &SearchCondition{SearchField: "2", Options: []*Options{{Label: "一级部门", Value: "1"}, {Label: "二级部门", Value: "2"}, {Label: "三级部门", Value: "3"}}},
			},
			{
				Title:         "漏洞超时未修复部门TOP10",
				Key:           "department_vul_not_fixed_top10",
				Type:          "bar",
				FlagCondition: &SearchCondition{SearchField: "2", Options: []*Options{{Label: "一级部门", Value: "1"}, {Label: "二级部门", Value: "2"}, {Label: "三级部门", Value: "3"}}},
			},
			{
				Title:           "漏洞修复率",
				Key:             "vul_fix_rate",
				Type:            "progress",
				SearchCondition: []*SearchCondition{{SearchField: "level", DefaultOption: "", Label: "漏洞等级", Options: []*Options{{Label: "全部", Value: ""}, {Label: "严重", Value: "4"}, {Label: "高危", Value: "3"}, {Label: "中危", Value: "2"}, {Label: "低危", Value: "1"}, {Label: "未知", Value: "5"}}}, {SearchField: "network_type", DefaultOption: "", Label: "网络类型", Options: []*Options{{Label: "全部", Value: ""}, {Label: "内网", Value: "1"}, {Label: "外网", Value: "0"}}}},
				FlagCondition:   &SearchCondition{SearchField: "", Options: []*Options{{Label: "全部", Value: ""}, {Label: "严重", Value: "4"}, {Label: "高危", Value: "3"}, {Label: "中危", Value: "2"}, {Label: "低危", Value: "1"}, {Label: "未知", Value: "5"}}},
			},
		},
	},
}

var OperationConfigList = []*DataBoardConfigList{
	{
		Title:         "探针情况概览",
		Key:           "probe_overview",
		Type:          "bar",
		FlagCondition: &SearchCondition{SearchField: "", Options: []*Options{}},
	},
	{
		Title:         "近一周上报任务概览",
		Key:           "weekly_task_report",
		Type:          "line",
		FlagCondition: &SearchCondition{SearchField: "", Options: []*Options{}},
	},
	{
		Title:         "异常任务列表",
		Key:           "exception_task_list",
		Type:          "table",
		FlagCondition: &SearchCondition{SearchField: "", Options: []*Options{}},
	},
	{
		Title:         "数据处理情况概览",
		Key:           "data_processing_overview",
		Type:          "bar",
		FlagCondition: &SearchCondition{SearchField: "", Options: []*Options{}},
	},
	{
		Title:         "近一个月资产发现趋势",
		Key:           "monthly_asset_discovery_trend",
		Type:          "line",
		FlagCondition: &SearchCondition{SearchField: "", Options: []*Options{}},
	},
}
