package custom_tags

import (
	"context"
	"errors"
	"fmt"
	"strconv"

	"github.com/olivere/elastic/v7"
	req "fobrain/fobrain/app/request/custom_tags"
	res "fobrain/fobrain/app/response/custom_tags"
	"fobrain/initialize/es"
	"fobrain/initialize/mysql"
	models "fobrain/models/mysql/custom_tags"
)

// Create create custom tags
func Create(params *req.InsertCustomTagsRequest) error {
	count, err := models.NewCustomTagsConfig().Count(mysql.WithWhere("tag_name = ?", params.TagName))
	if err != nil {
		return err
	}
	if count > 0 {
		return errors.New("标签已经存在，无法继续添加")
	}

	tagCreate := models.CustomTags{
		TagName: params.TagName,
	}
	err = models.NewCustomTagsConfig().CreateItem(&tagCreate)
	if err != nil {
		return err
	}
	return nil
}

// ALlList list custom tags
func ALlList(params *req.SearchCustomTagsRequest) ([]*res.List, int64, error) {
	var handleFuncs []mysql.HandleFunc
	if params.Keyword != "" {
		handleFuncs = append(handleFuncs, mysql.WithWhere("tag_name like ?", "%"+params.Keyword+"%"))
	}
	list, total, err := models.NewCustomTagsConfig().List(0, 0, handleFuncs...)
	if err != nil {
		return nil, 0, err
	}
	resList := make([]*res.List, 0)
	for _, tags := range list {
		resList = append(resList, &res.List{
			Name: tags.TagName,
			Id:   strconv.FormatUint(tags.Id, 10),
		})
	}
	return resList, total, nil
}

// Delete 删除标签并同步删除资产上的标签
func Delete(tagId string) error {
	// 1. 获取要删除的标签名称
	tag, err := models.NewCustomTagsConfig().FindById(tagId)
	if err != nil {
		return err
	}
	if tag == nil {
		return errors.New("标签不存在")
	}

	// 2. 删除资产上的该标签
	err = removeTagFromAssets(tag.TagName)
	if err != nil {
		return fmt.Errorf("删除资产标签失败: %v", err)
	}

	// 3. 删除标签记录
	err = models.NewCustomTagsConfig().DeleteById(tagId)
	if err != nil {
		return err
	}

	return nil
}

// removeTagFromAssets 从所有资产中移除指定标签
func removeTagFromAssets(tagName string) error {
	// 构建查询条件：查找包含该标签的资产
	query := elastic.NewTermQuery("tags", tagName)

	// 构建更新脚本：从 tags 数组中移除指定标签
	script := elastic.NewScript(`
		if (ctx._source.tags != null) {
			ctx._source.tags.removeIf(tag -> tag.equals(params.tagToRemove));
		}
		if (ctx._source.tag_source != null && ctx._source.tag_source.containsKey("0")) {
			String tagSource = ctx._source.tag_source["0"];
			if (tagSource != null && !tagSource.isEmpty()) {
				String[] tags = tagSource.split(",");
				List<String> newTags = new ArrayList<>();
				for (String tag : tags) {
					String trimmedTag = tag.trim();
					if (!trimmedTag.equals(params.tagToRemove)) {
						newTags.add(trimmedTag);
					}
				}
				ctx._source.tag_source["0"] = String.join(",", newTags);
			}
		}
	`).Param("tagToRemove", tagName)

	// 执行批量更新
	client := es.GetEsClient()
	_, err := client.UpdateByQuery("assets").
		Query(query).
		Script(script).
		Refresh("true").
		Do(context.Background())

	return err
}
