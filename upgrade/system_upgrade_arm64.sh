#!/bin/bash
set -e
trap 'echo "[Error] 发生错误,脚本退出"; update_progress 100 true "升级失败" false; exit 1' ERR

echo "-----------------------------------"
current_dir="/data/fobrain/storage/upgrade/exec-dir"
echo "[Info] Upgrade path: $current_dir"

#fobrainDir="/data/fobrain" #正式使用的目录
fobrainDir="/data/fobrain" #test环境使用的目录
echo "[Info] fobrainDir path: $fobrainDir"

echo "[Info] vueDir path: /data/dist"
echo "-----------------------------------"

fobrain_container_name='fobrain'

cd $fobrainDir
merge_service_container_name=$(docker ps --format "{{.Names}}" | grep merge-service | sed -n '1P')
cd $current_dir

fobrain_name='fobrain'
merge_service_name="mergeservice"
#cmd_name="cmd"
version_name="version"

pkgFobrainName='fobrain.server'
pkgMergeServiceName='merge-service.server'
pkgCmdName='cmd-migrate'

localConfigPath='/etc/fobrain/conf/config.json'

bin_path='/usr/sbin'
config_path='/etc/fobrain/conf'
#config_path="$current_dir/conf"
config_file='config.json'

heartbeat='127.0.0.1:8090/api/v1/heartbeat'
mergeHi='127.0.0.1:8090/api/v1/hi?name=system'

#cmd_docker_path="$fobrain_container_name:$bin_path/$cmd_name"
fobrain_docker_path="$fobrain_container_name:$bin_path/$fobrain_name"
mergeservice_docker_path="$merge_service_container_name:$bin_path/$merge_service_name"
fobrain_config_dokcer_path="$fobrain_container_name:$config_path/$config_file"
mergeservice_config_docker_path="$merge_service_container_name:$config_path/$config_file"
version_docker_path="$config_path/$version_name"
current_version_file="$current_dir/$version_name"

# 镜像操作相关函数
function load_images() {
  echo "[Info] 开始加载镜像文件"
  cd $current_dir  # 恢复到原目录
  if [ -d "build-images" ]; then
    if [ -f "build-images/fobrain.tar" ]; then
      echo "[Info] 加载fobrain镜像"
      docker load -i build-images/fobrain.tar
    fi
    if [ -f "build-images/elasticsearch.tar" ]; then
      echo "[Info] 加载elasticsearch镜像"
      docker load -i build-images/elasticsearch.tar
    fi
  else
    echo "[Info] 未找到build-images目录，跳过镜像加载"
  fi
  cd $fobrainDir
  # 强制清理可能存在的容器
  docker rm -f fobrain 2>/dev/null || true
  docker-compose up -d --no-deps fobrain
  # 强制清理可能存在的容器
  docker rm -f es 2>/dev/null || true
  docker-compose up -d --no-deps es
  docker-compose up -d --no-deps merge-service
  # 增加健康检查
  echo "[Info] 等待ES集群健康状态变为GREEN"
  local retries=60
  while [ $retries -gt 0 ]; do
    health=$(curl -s -m 10 "http://localhost:9200/_cluster/health" | grep -o '"status":"[^"]*"' | awk -F'"' '{print $4}')
    # 修改健康检查逻辑，允许YELLOW状态
    if [[ "$health" == "green" || "$health" == "yellow" ]]; then
      echo "[Info] ES集群状态已恢复为GREEN/YELLOW"
      break
    fi
    echo "[Info] 当前ES集群状态: $health，剩余重试次数: $retries"
    sleep 10
    ((retries--))
  done

  if [ $retries -eq 0 ]; then
    echo "[Error] ES集群未在600秒内恢复" >&2
  fi
  cd $current_dir  # 恢复到原目录
}
upgrade_process_file="/data/fobrain/storage/upgrade/upgrade_process"

function update_progress() {
    local process=$1
    local failed=${2:-false}
    local msg=${3:-""}
    local finish=${4:-false}
    local version=$(read_version "$current_version_file" false)

    # 创建 JSON 字符串
    local json_content="{\"version\":\"$version\",\"process\":$process,\"finish\":$finish,\"failed\":$failed,\"msg\":\"$msg\"}"

    # 确保目录存在
    mkdir -p "$(dirname "$upgrade_process_file")"

    # 直接将 JSON 写入文件，覆盖原有内容
    echo "$json_content" > "$upgrade_process_file"
}

function print_variables() {
  echo "[Info] 变量值如下:"
  echo "[Info] fobrain_docker_path: $fobrain_docker_path"
  echo "[Info] mergeservice_docker_path: $mergeservice_docker_path"
  echo "[Info] fobrain_config_dokcer_path: $fobrain_config_dokcer_path"
  echo "[Info] mergeservice_config_docker_path: $mergeservice_config_docker_path"
  echo "[Info] version_docker_path: $version_docker_path"
  echo "[Info] current_version_file: $current_version_file"
}

function versionValidate(){
  echo "------versionValidate start------"
  update_progress 5 false "开始版本验证"

  if [ ! -f "$current_version_file" ]; then
    update_progress 5 true "版本文件不存在" false
    echo "[Error] 版本文件不存在，请检查路径。"
    exit 1
  fi

  if ! docker exec "$fobrain_container_name" test -f "$version_docker_path"; then
    update_progress 5 true "容器内版本文件不存在" false
    echo "[Error] 容器内版本文件不存在，请检查路径。"
    exit 1
  fi

  # 提取版本号
  new_version=$(read_version "$current_version_file" false)
  old_version=$(read_version "$version_docker_path" true "$fobrain_container_name")

  echo "[Info] 准备比较版本："
  echo "[Info] 当前版本: $old_version"
  echo "[Info] 新版本: $new_version"

  set +e
  trap - ERR

  compare_versions "$old_version" "$new_version"
  result=$?

  if [[ $result -eq 1 ]]; then
    echo "[Info] 新版本号 $new_version 大于旧版本号 $old_version,版本升级可用。"
  elif [[ $result -eq 255 ]]; then
    echo "[Info] 旧版本号 $old_version 大于新版本号 $new_version,需要升级。"
    update_progress 100 false "旧版本号 $old_version 大于新版本号 $new_version,不需要升级" true
    exit 0
  else
    echo "[Info] 新版本号和旧版本号相同。"
    update_progress 100 false "新版本号和旧版本号相同" true
    exit 0
  fi

  echo "[Info] 继续执行后续操作,验证版本依赖..."

  # 检查版本依赖
  dependencies=$(base64 -d < "$current_version_file" | sed -n 's/.*"version_dependencies":\s*\[\(.*\)\].*/\1/p' | tr -d '"' | tr ',' ' ')
  for dep in $dependencies; do
    result=$(compare_versions "$old_version" "$dep")
    if [[ $result -eq 1 ]]; then
      echo "[Info] 当前版本过低,需要先升级前置版本$dep"
      update_progress 100 true "当前版本过低,需要先升级前置版本$dep" false
      exit 1
    fi
  done

  echo "-----版本所有校验通过，可以执行升级----"
  update_progress 10 false "版本验证通过"
}

# 读取文件并进行Base64解码的函数
function read_version() {
    local file_path="$1"
    local is_docker_path="$2"
    local container_name="$3"

    if [ "$is_docker_path" = "true" ]; then
        # 从Docker容器中读取文件
        if ! docker exec "$container_name" test -f "$file_path"; then
            echo "[Error] Docker容器 $container_name 中版本文件不存在：$file_path" >&2
            exit 1
        fi
        version=$(docker exec "$container_name" bash -c "base64 --decode < $file_path | awk -F'\"release_version\":' '{ print \$2 }' | awk -F'\"' '{ print \$2 }'")
    else
        # 从宿主机读取文件
        if [ ! -f "$file_path" ]; then
            echo "[Error] 宿主机版本文件不存在：$file_path" >&2
            exit 1
        fi
        version=$(base64 --decode < "$file_path" | awk -F'"release_version":' '{ print $2 }' | awk -F'"' '{ print $2 }')
    fi

    echo "$version"
}

function compare_versions() {
    local old_version="$1"
    local new_version="$2"

    IFS='.' read -r old_major old_minor old_patch <<< "$old_version"
    IFS='.' read -r new_major new_minor new_patch <<< "$new_version"

    if [[ "$new_major" -gt "$old_major" ]]; then
        return 1
    elif [[ "$new_major" -lt "$old_major" ]]; then
        return 255
    fi

    if [[ "$new_minor" -gt "$old_minor" ]]; then
        return 1
    elif [[ "$new_minor" -lt "$old_minor" ]]; then
        return 255
    fi

    if [[ "$new_patch" -gt "$old_patch" ]]; then
        return 1
    elif [[ "$new_patch" -lt "$old_patch" ]]; then
        return 255
    fi

    return 0
}

function upgradeHandle() {
  echo "------upgradeHandle start------"
  update_progress 15 false "开始升级处理"

  

  if [ -e "$pkgFobrainName" ]; then
    echo "[Info] 处理fobrain images"
    docker rmi fobrain-arm64:old
    docker tag fobrain-arm64:latest fobrain-arm64:old
    
    docker rmi elasticsearch:old
    docker tag elasticsearch:7.17.26 elasticsearch:old
    # 停止并删除 es 服务容器
    docker-compose stop es && docker rm -f es
    # 备份Elasticsearch数据
    echo "[Info] 备份Elasticsearch数据"
    ES_BACKUP_DIR="/data/fobrain/storage/data/es/nodes-backup-$(date +%Y%m%d_%H%M%S)"
    if [ -d "/data/fobrain/storage/data/es/nodes" ]; then
      # 创建备份目录
      mkdir -p "$ES_BACKUP_DIR" 2>/dev/null || true
      # 只备份nodes目录（包含所有索引数据和集群状态）
      cp -r /data/fobrain/storage/data/es/nodes "$ES_BACKUP_DIR/" 2>/dev/null || true
      echo "[Info] Elasticsearch nodes数据已备份到: $ES_BACKUP_DIR"
      # 记录备份路径供回滚使用
      echo "$ES_BACKUP_DIR" > "$current_dir/es-backup-path"
    fi
    # 删除 es 的镜像
    docker rmi elasticsearch:7.17.26
    # 停止并删除 fobrain 服务容器
    docker-compose stop fobrain && docker rm -f fobrain
    # 删除 fobrain 的镜像（假设名字为 fobrain:latest）
    docker rmi fobrain-arm64:latest
    cd $current_dir
    if [ -e  "docker-compose.yaml" ]; then
         echo "处理docker-compose.yam"
         rm -rf /data/fobrain/docker-compose.yaml
         cp docker-compose.yaml /data/fobrain/docker-compose.yaml
    fi
    cd $fobrainDir
    load_images
    cd $current_dir
    echo "[Info] 更新fobrain 二进制文件"
    if ! docker cp $pkgFobrainName $fobrain_docker_path; then
        echo "[Error] 复制新的 fobrain 二进制文件失败" >&2
        update_progress 15 true "复制新的 fobrain 二进制文件失败" false
        exit 1
    fi
    echo "复制version文件到fobrain容器并commit到image"
    docker cp "$current_version_file" "$fobrain_container_name:$version_docker_path"
    echo "[Info] 处理fobrain docketr commit"
    docker commit fobrain fobrain-arm64:latest
    update_progress 25 false "处理fobrain镜像成功"
  fi

   docker cp ./old-version "$fobrain_container_name:$version_docker_path"

  if [ -e "$pkgMergeServiceName" ]; then
    echo "[Info] 处理merge-service images"
    docker rmi merge-service-arm64:old
    docker tag merge-service-arm64:latest merge-service-arm64:old

    echo "[Info] 更新merge-service 二进制文件"
    if ! docker cp $pkgMergeServiceName $mergeservice_docker_path; then
      echo "[Error] 复制新的 merge service 二进制文件失败" >&2
      update_progress 25 true "复制新的 merge service 二进制文件失败" false
      exit 1
    fi
    echo "[Info] merge-service docker commit"
    docker commit $merge_service_container_name merge-service-arm64:latest
    update_progress 35 false "处理merge-service镜像成功"
  fi

  if [ -e "$pkgFobrainName" ]; then
    echo "[Info] 停止fobrain容器"
    cd $fobrainDir
    docker-compose stop $fobrain_container_name
    sleep 3
    docker-compose down $fobrain_container_name
    echo "[Info] 停止fobrain容器成功"
  fi

  cd $current_dir
  if [ -e "$pkgMergeServiceName" ]; then
    echo "[Info] 停止merge-service容器"
    cd $fobrainDir
    docker-compose stop merge-service
    sleep 3
    docker-compose down merge-service
    echo "[Info] 停止merge-service容器成功"
  fi

  cd $current_dir
  update_progress 40 false "开始处理cmd migrate"
  if [ -e "$pkgCmdName" ]; then
    echo "[Info] data migrate ---"
    if [ -e "$config_file" ]; then
      if [ -e "$localConfigPath" ]; then
        echo "[Info]备份替换etc下config.json"
        cp $localConfigPath ./data-config.json
        cp $config_file $localConfigPath
      fi
    fi
    cd $current_dir
    chmod +x cmd-migrate
    if ! ./cmd-migrate  migrate run; then
      echo "[Error] 运行 cmd migrate run -c all 失败" >&2
      
      update_progress 40 true "运行 cmd migrate run -c all 失败" false
      rollback
      exit 1
    fi
    
    echo "[Info] data migrate end----"
  fi

#  if [ -e  "docker-compose.yaml" ]; then
#     echo "处理docker-compose.yam"
#     rm -rf /data/fobrain/docker-compose.yaml
#     cp docker-compose.yaml /data/fobrain/docker-compose.yaml
#  fi

  cd $current_dir
  update_progress 45 false "开始重新启动fobrain"
  if [ -e "$pkgFobrainName" ]; then
    echo "[Info] 重新启动fobrain--"
    cd $fobrainDir
    if ! docker-compose up -d --no-deps fobrain; then
      echo "[Error] 重新启动fobrain失败" >&2
      update_progress 45 true "重新启动fobrain失败，回滚" false
      rollback
      exit 1
    fi
    # 复制到 fobrain 容器
    echo "[Info] 复制 cmd-migrate 到 fobrain 容器"
    cd $current_dir
    docker cp ./cmd-migrate $fobrain_container_name:/usr/sbin/cmd
    docker exec $fobrain_container_name chmod +x /usr/sbin/cmd
    echo "[Info] 重新启动fobrain 成功"

  fi

  cd $current_dir
  update_progress 55 false "开始重新启动merge-service"
  if [ -e "$pkgMergeServiceName" ]; then
    echo "[Info] 重新启动merge-service--"
    cd $fobrainDir
    if ! docker-compose up -d --no-deps merge-service; then
      echo "[Error] 重新启动merge-service失败" >&2
      update_progress 55 true "重新启动merge-service失败" false
      rollback
      exit 1
    fi
    echo "[Info] 重新启动merge-service 成功"
    cd $current_dir
  fi

  update_progress 65 false "等待90秒让服务完全启动"
  echo "[Info] 等待90秒让服务完全启动"
  sleep 90

  # 然后进行接口验证
  if [ -e "$pkgFobrainName" ]; then
    update_progress 70 false "正在验证fobrain服务"
    echo "[Info] 验证 fobrain 心跳"
    response=$(curl -s -w "%{http_code}" -o /tmp/response_body.txt "$heartbeat")
    http_code="${response: -3}"
    response_body=$(cat /tmp/response_body.txt)

    echo "[Info] HTTP 状态码: $http_code"
    echo "[Info] 响应内容: $response_body"

    if [ "$http_code" -eq 200 ]; then
      echo "[Info] fobrain 升级成功"
      update_progress 70 false "fobrain升级成功"
    elif echo "$response_body" | grep -q "Success"; then
      echo "[Info] fobrain 升级成功"
      update_progress 70 false "fobrain升级成功"
    elif [ "$http_code" -eq 000 ]; then
      echo "[Info] fobrain 升级成功"
      update_progress 70 false "fobrain升级成功"
    else
      echo "[Error] 健康检查失败。升级失败，准备回滚"
      update_progress 70 true "fobrain健康检查失败，准备回滚" false
      rollback
      exit 2
    fi
  fi

  if [ -e "$pkgMergeServiceName" ]; then
    update_progress 80 false "正在验证merge_service"
    echo "[Info] 验证 merge_service"
    response=$(curl -s -w "%{http_code}" -o /tmp/response_body.txt "$mergeHi")
    http_code="${response: -3}"
    response_body=$(cat /tmp/response_body.txt)

    echo "[Info] HTTP 状态码: $http_code"
    echo "[Info] 响应内容: $response_body"

    if [ "$http_code" -eq 200 ]; then
      echo "[Info] merge_service 升级成功"
      update_progress 90 false "merge_service升级成功"
    elif echo "$response_body" | grep -q "Success"; then
      echo "[Info] merge_service 升级成功"
      update_progress 90 false "merge_service升级成功"
    elif [ "$http_code" -eq 000 ]; then
      echo "[Info] merge_service 升级成功"
      update_progress 90 false "merge_service升级成功"
    else
      echo "[Error] 健康检查失败。升级失败，准备回滚"
      update_progress 90 true "merge_service健康检查失败，准备回滚" false
      rollback
      exit 2
    fi
  fi

  echo "------所有服务升级和验证完成,复制version文件------"
  update_progress 90 false "所有服务升级和验证完成" false

  docker cp "$current_version_file" "$fobrain_container_name:$version_docker_path"
  echo "------upgradeHandle all end------"
}

function rollback(){
  update_progress 90 true "开始回滚" false
   echo "------rollback start------"
  if [ -e "$pkgCmdName" ]; then
    echo "[Info] data migrate rollback"
      cd $current_dir && $pkgCmdName migrate rollback
    echo "------data migrate rollback end------"
  fi

  if [ -e "$pkgFobrainName" ]; then
    cd $fobrainDir
    docker-compose down $fobrain_container_name
    #失败后回滚
    docker rmi $fobrain_container_name:latest
    docker tag $fobrain_container_name:old $fobrain_container_name:latest
    # 强制清理可能存在的容器
    docker rm -f fobrain 2>/dev/null || true
    docker-compose up -d --no-deps $fobrain_container_name
    echo "------fobrain rollback end------"

    docker-compose down es
    docker rmi es-arm64:7.17.26
    docker tag elasticsearch:old elasticsearch:7.17.26
    # 强制清理可能存在的容器
    docker rm -f es 2>/dev/null || true
    
    # 恢复Elasticsearch数据
    echo "[Info] 恢复Elasticsearch数据"
    if [ -f "$current_dir/es-backup-path" ]; then
      ES_BACKUP_PATH=$(cat "$current_dir/es-backup-path")
      # 检查备份目录是否存在且包含ES关键数据
      if [ -d "$ES_BACKUP_PATH/nodes" ] && [ "$(find "$ES_BACKUP_PATH/nodes" -name "*.dat" -o -name "segments_*" | head -1)" ]; then
        echo "[Info] 发现有效的Elasticsearch nodes备份数据: $ES_BACKUP_PATH"
        echo "[Info] 从备份恢复Elasticsearch nodes数据"
        rm -rf /data/fobrain/storage/data/es/nodes 2>/dev/null || true
        cp -r "$ES_BACKUP_PATH/nodes" /data/fobrain/storage/data/es/nodes 2>/dev/null || true
        sudo chown -R 1000:1000 /data/fobrain/storage/data/es
        echo "[Info] Elasticsearch nodes数据恢复完成"
      else
        echo "[Warning] 备份目录不存在或不包含有效的ES数据: $ES_BACKUP_PATH"
        echo "[Warning] 为避免数据丢失，不清理现有数据，可能需要手动处理版本兼容性问题"
      fi
    else
      echo "[Warning] 未找到备份路径文件"
      echo "[Warning] 为避免数据丢失，不清理现有数据，可能需要手动处理版本兼容性问题"
    fi
    
    docker-compose up -d --no-deps es
    sleep 60
    docker-compose up -d --no-deps $fobrain_container_name
    cd $current_dir
  fi

  if [ -e "$pkgMergeServiceName" ]; then
    cd $fobrainDir
    docker-compose down merge-service
    #失败后回滚
    docker rmi merge-service:latest
    docker tag merge-service:old merge-service:latest
      docker-compose up -d --no-deps merge-service
    echo "------mergeService rollback end------"
    cd $current_dir
  fi

  if [ -e "$config_file" ]; then
    if [ -e "$localConfigPath" ]; then
      cd $current_dir
      echo "[Info]还原etc下config.json"
      cp  ./data-config.json $localConfigPath
    fi
  fi
  echo "------rollback all end------"
}

function upgradeVue(){
  echo "------ upgradeVue vue dist start ----------"
  update_progress 92 false "开始升级Vue"
  cd "$current_dir" || exit
  echo "[Info] upgradeVue 当前目录: $(pwd)"

  # 创建备份目录
  backup_dir="$current_dir/dist-old"
  mkdir -p "$backup_dir"

  # 备份 vueDir 内容到 dist-old
  cp -rf /data/dist/* "$backup_dir"
  echo "[Info] 已备份 $vueDir 内容 $backup_dir"

  #解压到dist文件夹
  rm -rf /data/dist
  tar --warning=no-unknown-keyword -xvzf dist.tar.gz -C /data/
  docker restart nginx

  echo "------ upgradeVue end ----------"
  update_progress 96 false "Vue升级完成"
}

# 主脚本逻辑
update_progress 0 false "开始升级" false
#打印变量
print_variables
#版本校验
versionValidate
#备份hosts
docker cp fobrain:/etc/hosts ./hosts-fobrain
#备份旧version
docker cp  "$fobrain_container_name:$version_docker_path" ./old-version

cd "$current_dir"
if [[ -e "$pkgMergeServiceName" || -e "$pkgFobrainName" ]]; then
  cd "$current_dir"
  upgradeHandle
fi

# 处理 public 文件夹
if [ -d "public" ]; then
   echo "[Info] 发现 public 文件夹，开始复制到 /data/fobrain/storage/app/public"
  cp -rf public/icon/*  /data/fobrain/storage/app/public/icon
  cp -rf public/logo/*  /data/fobrain/storage/app/public/logo
  cp -rf public/template/*  /data/fobrain/storage/app/public/template
  chmod -R 777 /data/fobrain/storage/app/public/
  echo "[Info] public 文件夹复制完成"
fi

if [ -e "dist.tar.gz" ]; then
  upgradeVue
  cd "$current_dir"
fi
sudo chown -R 1000:1000 /data/fobrain/storage/data/es
sudo chown -R 1000:1000 /data/fobrain/storage/backup
sudo chmod -R 750 /data/fobrain/storage/backup
cd $current_dir
echo "--保证数据表存在在执行一次migrate,sleep 15秒--"
sleep 15
./cmd-migrate  migrate run -c es
./cmd-migrate  migrate run -c mysql

#恢复hosts
cat hosts-fobrain | docker exec -i fobrain sh -c 'cat > /etc/hosts'
docker exec fobrain cat /etc/hosts
docker cp "$current_version_file" "$fobrain_container_name:$version_docker_path"

update_progress 100 false "升级完成" true
echo "------ all end ----------"