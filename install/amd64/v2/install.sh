#!/bin/bash

# 检查是否有管理员权限
if [ "$(id -u)" -ne 0 ]; then
  echo "请使用 root 用户或具有管理员权限的用户执行此脚本。"
  exit 1
fi


function check_environment() {
	# 检查 Docker 是否已安装,如果已经安装docker，还要持续安装，建议手动提取安装脚本内容，进行手动安装。
	rpm -qa | grep -q docker
	if [ $? -eq 0 ]; then
		echo "你已经安装了Docker"&& exit 1
    exit 1
	fi

	# 检查443 80 3306端口是否已占用
	# 遍历端口列表，逐个检查
	ports=(80 443 9200 3306 6379 8090 8091)
	for port in "${ports[@]}"; do
	  if ss -ltn sport =  ":$port" | grep -v State; then
	    echo "80 443 9200 3306 6379 8500 8090 8091有端口占用的情况。" && exit 1
	  fi
	done


	cpu_thread_count=16
	# 检查CPU线程数
	cpu=$(grep 'processor' /proc/cpuinfo | sort -u | wc -l)
	if [ $cpu -lt $cpu_thread_count ]; then
		echo "当前系统的CPU线程数是$cpu，不符合软件的安装条件，请联系相关负责人" && exit 1
	fi

	memsize=32000000
	# 检查系统内存
	MemTotal=$(grep MemTotal /proc/meminfo | awk '{print $2}')
	if [ $MemTotal -lt $memsize ]; then
		echo "当前系统内存，不符合软件的安装条件，请联系相关负责人" && exit 1
	    exit 1
	fi
}

check_environment

function checkKernelParams() {
  # 必须以 root 权限运行
  [ "$(id -u)" -ne 0 ] && { echo "必须以 root 权限运行此脚本"; exit 1; }

  SYSCTL_CONF="/etc/sysctl.conf"
  PARAM_FILE="/tmp/desired_params.txt"

  # 备份当前 sysctl.conf
  cp "$SYSCTL_CONF" "${SYSCTL_CONF}.bak"

  # 将期望的参数写入临时文件 注意此处here doc 前面不能有空格
  cat <<'EOF' > "$PARAM_FILE"
net.ipv4.tcp_fin_timeout 30
net.ipv4.tcp_tw_reuse 1
net.ipv4.ip_local_port_range 1024 65000
net.ipv4.tcp_max_tw_buckets 5000
net.ipv4.tcp_synack_retries 2
net.ipv4.tcp_syn_retries 2
net.nf_conntrack_max 2621440
fs.file-max 2097152
EOF

  updated=0

  # 从临时文件中逐行读取（避免管道导致的子 shell 问题）
  while IFS=' ' read -r key rest; do
      # rest 为期望的值（可能包含空格，比如 ip_local_port_range）
      desired_value="$rest"
      if [ "$key" = "net.ipv4.ip_local_port_range" ]; then
          # 对于此参数，标准化空格
          desired_value=$(echo "$desired_value" | tr -s ' ')
      fi

      # 获取当前值，并标准化空格（防止格式不一致）
      current_value=$(sysctl -n "$key" 2>/dev/null | tr -s ' ')
      if [ $? -ne 0 ]; then
          echo "参数 $key 不存在，跳过。"
          continue
      fi

      if [ "$current_value" != "$desired_value" ]; then
          echo "更新 $key: 当前值 ($current_value) -> 期望值 ($desired_value)"
          sysctl -w "$key=$desired_value"
          updated=1
      else
          echo "$key 已经设置为 $desired_value"
      fi

      # 更新 /etc/sysctl.conf：
      # 先删除该参数的已有行（格式为 key=...，注意等号两侧不允许有空格）
      sed -i "/^$key[[:space:]]*=/d" "$SYSCTL_CONF"
      # 追加新的设置（确保格式为 key=value）
      echo "$key=$desired_value" >> "$SYSCTL_CONF"
  done < "$PARAM_FILE"

  if [ $updated -eq 1 ]; then
      echo "更新 /etc/sysctl.conf 后执行 sysctl -p 输出："
      sysctl -p
  else
      echo "所有内核参数已是期望值，无需更新。"
  fi

  rm -f "$PARAM_FILE"
}

#checkKernelParams

# 定义安装日志文件
current_dir="$PWD"
log_file=${current_dir}/install_`date +%s`.log


# 检查当前的 SELinux 模式
current_mode=$(getenforce)
if [ "$current_mode" == "Enforcing" ]; then
    echo "SELinux 当前模式为 Enforcing，正在设置为 Permissive..."
    setenforce 0
    echo "成功将 SELinux 模式更改为 Permissive。"
else
    echo "SELinux 当前模式为 $current_mode，无需更改。"
fi

# 时间同步确认
echo "当前系统时间是: $(date)" | tee -a ${log_file}
echo -n "是否需要设置系统时间？(y/n): " | tee -a ${log_file}
read answer

if [[ "$answer" =~ ^[Yy]$ ]]; then
    echo "请输入正确的时间 (格式: YYYY-MM-DD HH:MM:SS): " | tee -a ${log_file}
    read date_input

    # 验证输入的时间格式
    if [[ $date_input =~ ^[0-9]{4}-[0-9]{2}-[0-9]{2}\ [0-9]{2}:[0-9]{2}:[0-9]{2}$ ]]; then
        # 设置系统时间
        if sudo date -s "$date_input" 2>&1 | tee -a ${log_file}; then
            # 将系统时间同步到硬件时钟
            sudo hwclock --systohc 2>&1 | tee -a ${log_file}
            echo "系统时间设置成功。当前时间: $(date)" | tee -a ${log_file}
        else
            echo "时间设置失败，请检查输入格式是否正确" | tee -a ${log_file}
            exit 1
        fi
    else
        echo "输入的时间格式不正确，请使用格式: YYYY-MM-DD HH:MM:SS" | tee -a ${log_file}
        exit 1
    fi
else
    echo "跳过时间设置，继续安装流程..." | tee -a ${log_file}
fi

# 创建所需目录
echo "创建所需目录..." | tee -a ${log_file}
directories=(
    "/data"
    "/data/dist"
    "/data/certs"
    "/data/fobrain"
    "/data/fobrain/storage"
    "/data/fobrain/storage/upgrade"
    "/data/fobrain/storage/upgrade/flag-dir"
    "/data/fobrain/storage/logs"
    "/data/fobrain/storage/files"
    "/data/fobrain/fswatch"
    "/data/fobrain/storage/data"
    "/data/fobrain/storage/data/mysql"
    "/data/fobrain/storage/logs/mysql"
    "/data/fobrain/storage/data/redis"
    "/data/fobrain/storage/logs/redis"
    "/data/fobrain/storage/logs/es"
    "/data/fobrain/storage/data/es"
    "/data/fobrain/storage/consul_data"
    "/data/fobrain/storage/conf"
    "/data/fobrain/storage/conf/mysql"
    "/data/fobrain/storage/ftp"
    #"/data/fobrain/storage/conf/mysql/conf.d"
    "/data/fobrain/storage/app"
    "/data/storage"
    "/data/storage/files"
    "/data/storage/ftp"
    "/data/storage/logs"
    "/data/storage/logs/nginx"
    "/etc/fobrain"
    "/etc/fobrain/conf"
    "/etc/nginx"
    "/data/docker"
)

for dir in "${directories[@]}"; do
    if [ ! -d "$dir" ]; then
        echo "创建目录 $dir ..." | tee -a ${log_file}
        mkdir -p "$dir" 2>&1 | tee -a ${log_file}
    else
        echo "目录 $dir 已存在。" | tee -a ${log_file}
    fi
    # 递归设置目录权限
    chmod -R 777 "$dir" 2>&1 | tee -a ${log_file}
done

# 特别确保 ES 相关目录的权限
echo "设置 Elasticsearch 目录权限..." | tee -a ${log_file}
touch /data/fobrain/storage/logs/es/gc.log
sudo chmod -R 777 /data/fobrain/storage/logs/es/gc.log
sudo chmod -R 777 /data/fobrain/storage/logs/es
sudo chmod -R 777 /data/fobrain/storage/data/es
sudo chmod -R 777 /data/fobrain/storage

ln -s /data/docker /var/lib/docker
#cp docker-daemon.json /data/docker/daemon.json
#sudo chmod -R 777 /data/docker/daemon.json

# 复制必要的配置文件
echo "复制配置文件..." | tee -a ${log_file}

copy_file() {
    # 检查源文件是否存在
    if [ ! -e "$1" ]; then
        echo "源文件 $1 不存在，退出安装..." | tee -a ${log_file}
        exit 1
    fi
    
    # 确保目标目录存在
    target_dir=$(dirname "$2")
    if [ ! -d "$target_dir" ]; then
        echo "创建目标目录 $target_dir ..." | tee -a ${log_file}
        mkdir -p "$target_dir" 2>&1 | tee -a ${log_file}
    fi

    # 执行复制操作
    sudo cp -rf "$1" "$2" 2>&1 | tee -a ${log_file}
    if [ $? -ne 0 ]; then
        echo "复制文件 $1 到 $2 失败，退出安装..." | tee -a ${log_file}
        exit 1
    fi
    
    # 设置权限
    if [ -d "$2" ]; then
        # 如果是目录，递归设置权限
        sudo chmod -R 777 "$2" 2>&1 | tee -a ${log_file}
    else
        # 如果是文件，直接设置权限
        sudo chmod 777 "$2" 2>&1 | tee -a ${log_file}
    fi
    
    echo "成功复制并设置权限 $1 到 $2" | tee -a ${log_file}
}

# 文件复制操作保持不变
copy_file docker-compose.yaml /data/fobrain/
copy_file config.json /etc/fobrain/conf/
copy_file public /data/fobrain/storage/app/
copy_file fonts /data/fobrain/storage/app/
copy_file ip_mapping /data/fobrain/storage/app/
copy_file fileboy_script.sh /data/fobrain/storage/upgrade/
copy_file filegirl.yaml /data/fobrain/storage/upgrade/

sudo cp fileboy /usr/local/bin/
sudo chmod 777 /usr/local/bin/fileboy

# 安装Docker，配置Docker，设置systemd启动
echo "安装Docker 26.1.4..." | tee -a ${log_file}
tar xzvf docker-26.1.4.tgz 2>&1 | tee -a ${log_file}
sudo cp docker/* /usr/bin/ 2>&1 | tee -a ${log_file}
sudo cp docker.service /etc/systemd/system/docker.service 2>&1 | tee -a ${log_file}
sudo cp fileboy.service /etc/systemd/system/fileboy.service  2>&1 | tee -a ${log_file}
sudo systemctl daemon-reload 2>&1 | tee -a ${log_file}
sudo systemctl enable docker 2>&1 | tee -a ${log_file}
sudo systemctl start docker 2>&1 | tee -a ${log_file}
sudo systemctl enable fileboy 2>&1 | tee -a ${log_file}
sudo systemctl start fileboy 2>&1 | tee -a ${log_file}

# 验证 Docker 是否安装成功
docker_version=$(docker --version 2>/dev/null)
if [ $? -eq 0 ]; then
  echo "Docker 安装成功，版本信息：$docker_version" 2>&1 | tee -a ${log_file}
else
  echo "Docker 安装失败。" 2>&1 | tee -a ${log_file}
  exit 1
fi

sudo cp docker-compose-linux-x86_64 /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
# 验证 Docker Compose 是否安装成功
docker_compose_version=$(docker-compose --version 2>/dev/null)
if [ $? -eq 0 ]; then
  echo "Docker Compose 安装成功，版本信息：$docker_compose_version" 2>&1 | tee -a ${log_file}
else
  echo "Docker Compose 安装失败。" 2>&1 | tee -a ${log_file}
  exit 1
fi

# Docker Swarm 初始化
#echo "初始化 Docker Swarm..." | tee -a ${log_file}
#docker swarm init 2>&1 | tee -a ${log_file}

# 导入 Docker 镜像
echo "导入 Docker 镜像..." | tee -a ${log_file}
docker load -i fobrain.tar 2>&1 | tee -a ${log_file}
docker load -i merge-service.tar 2>&1 | tee -a ${log_file}
docker load -i es-amd64.tar 2>&1 | tee -a ${log_file}
docker load -i mysql-8.0.38.tar 2>&1 | tee -a ${log_file}
docker load -i redis-6.2.1.tar 2>&1 | tee -a ${log_file}
docker load -i nginx.tar 2>&1 | tee -a ${log_file}

sudo cp nginx/certs/* /data/certs/
sudo chmod 766 /data/certs/*
sudo chown nginx:nginx /data/certs/fobrain.pem
sudo chmod 766 /data/certs/fobrain.pem

echo "所有配置文件复制成功。" | tee -a ${log_file}

# 复制前端代码到指定目录
echo "复制前端代码到 /data/fobrain/dist 目录..." | tee -a ${log_file}
tar --warning=no-unknown-keyword -xvzf dist.tar.gz -C /data/  2>&1 | tee -a ${log_file}

# 安装Nginx
echo "安装Nginx..." | tee -a ${log_file}
# 首先检查是否已安装nginx
if rpm -q nginx > /dev/null; then
    echo "Nginx已安装,跟fobrain安装程序冲突,请先卸载" | tee -a ${log_file}
    exit 1
fi

mv /etc/nginx/nginx.conf /etc/nginx/nginx.conf.bak
mv /etc/nginx/mime.types /etc/nginx/mime.types.bak 
cp nginx/conf/nginx.conf  /etc/nginx/
cp nginx/conf/mime.types  /etc/nginx/
echo "nginx处理结束..."

cd /data/fobrain/
pwd
# 启动 MySQL、ES、Redis 容器，使用默认账户密码
echo "启动 Nginx、MySQL、Elasticsearch、Redis..." | tee -a ${log_file}
docker-compose up -d --no-deps nginx mysql redis es 2>&1 | tee -a ${log_file}
# 检查 docker-compose 命令是否成功
if [ "${PIPESTATUS[0]}" -ne 0 ]; then
    echo "docker-compose nginx mysql redis es启动失败，退出..." | tee -a ${log_file}
    exit 1
else
    echo "docker-compose nginx mysql redis es服务成功启动。" | tee -a ${log_file}
fi

echo "等待容器跑起来"
sleep 10
docker ps
sleep 20
docker ps
sleep 40
docker ps
sleep 80
docker ps

cd $current_dir
pwd
# 执行 cmd migrate run -c all
echo "执行数据库迁移..." | tee -a ${log_file}
chmod +x cmd-migrate

echo "执行 cmd-migrate..." | tee -a ${log_file}
sudo ./cmd-migrate migrate run -c all 2>&1 | tee -a ${log_file}
if [ ${PIPESTATUS[0]} -ne 0 ]; then
    echo "cmd-migrate 首次执行失败。等待 160 秒后重试..." | tee -a ${log_file}
    sleep 160
    echo "重新执行 cmd-migrate..." | tee -a ${log_file}
    sudo ./cmd-migrate migrate run -c all 2>&1 | tee -a ${log_file}
    
    if [ ${PIPESTATUS[0]} -ne 0 ]; then
        echo "cmd-migrate 在重试后仍然失败，退出..." | tee -a ${log_file}
        exit 1
    fi
fi

echo "cmd-migrate 执行成功。" | tee -a ${log_file}

cd /data/fobrain/
pwd
# Docker Compose 启动所有服务
echo "使用 Docker Compose 启动服务..." | tee -a ${log_file}
docker-compose up -d --no-deps fobrain merge-service 2>&1 | tee -a ${log_file}
# 检查 docker-compose 命令是否成功
if [ "${PIPESTATUS[0]}" -ne 0 ]; then
    echo "docker-compose 服务启动失败，退出..." | tee -a ${log_file}
    exit 1
else
    echo "docker-compose 服务成功启动。" | tee -a ${log_file}
fi

sleep 10

sudo chown -R 1000:1000 /data/fobrain/storage/backup
sudo chmod -R 750 /data/fobrain/storage/backup

cd $current_dir
pwd
docker cp version fobrain:/etc/fobrain/conf/version
systemctl restart fileboy 2>&1 | tee -a ${log_file}
echo "所有步骤执行完成。" | tee -a ${log_file}

