package custom_tags

import (
	"errors"
	"fobrain/initialize/mysql"
	"gorm.io/gorm"
)

type CustomTags struct {
	mysql.BaseModel
	TagName string `json:"tag_name" gorm:"tag_name"` // 自定义标签名称
}

func (c *CustomTags) TableName() string {
	return "custom_tags"
}

func NewCustomTagsConfig() *CustomTags {
	return &CustomTags{BaseModel: mysql.BaseModel{DB: mysql.GetDbClient().Table((&CustomTags{}).TableName())}}
}

// List 自定义标签列表
func (c *CustomTags) List(page, size int, opts ...mysql.HandleFunc) ([]*CustomTags, int64, error) {
	query := c.DB.Table(c.TableName())
	for _, opt := range opts {
		opt(query)
	}

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if !mysql.IsPageAll(page, size) {
		query.Scopes(mysql.PageLimit(page, size))
	}

	var list = make([]*CustomTags, 0)
	if err := query.Find(&list).Error; err != nil {
		return nil, 0, err
	}
	return list, total, nil
}

// CreateItem 创建自定义标签
func (c *CustomTags) CreateItem(item *CustomTags) error {
	return c.DB.Model(&CustomTags{}).Create(item).Error
}

// Count 自定义标签数量
func (c *CustomTags) Count(opts ...mysql.HandleFunc) (int64, error) {
	query := c.DB.Table(c.TableName())
	for _, opt := range opts {
		opt(query)
	}

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return 0, err
	}
	return total, nil
}

// FindById 根据ID查找标签
func (c *CustomTags) FindById(id string) (*CustomTags, error) {
	var tag CustomTags
	err := c.DB.Where("id = ?", id).First(&tag).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &tag, nil
}

// DeleteById 根据ID删除标签
func (c *CustomTags) DeleteById(id string) error {
	return c.DB.Where("id = ?", id).Delete(&CustomTags{}).Error
}
