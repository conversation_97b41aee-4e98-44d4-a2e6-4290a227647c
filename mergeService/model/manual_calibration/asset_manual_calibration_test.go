package model

import (
	"fobrain/initialize/mysql"
	"fobrain/models/elastic/assets"
	"fobrain/models/mysql/personnel_departments"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestAssetManualCalibration_UpdateMergeResult(t *testing.T) {
	tests := []struct {
		name         string
		mergeResult  *assets.Assets
		originalData *assets.Assets
		wantErr      bool
	}{
		{
			name:         "空参数测试",
			mergeResult:  nil,
			originalData: nil,
			wantErr:      true,
		},
		{
			name: "更新业务系统",
			mergeResult: &assets.Assets{
				Business: []*assets.Business{
					{System: "旧系统", Owner: "旧负责人"},
				},
				BusinessSource: map[string][]*assets.Business{},
				AllSourceIds:   []uint64{},
				SourceIds:      []uint64{},
			},
			originalData: &assets.Assets{
				Business: []*assets.Business{
					{System: "新系统", Owner: "新负责人"},
				},
				BusinessSource: map[string][]*assets.Business{
					"0": {
						{System: "新系统", Owner: "新负责人"},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "更新运维人员",
			mergeResult: &assets.Assets{
				OperInfo:     []*assets.PersonBase{},
				OperSource:   map[string]string{},
				AllSourceIds: []uint64{},
				SourceIds:    []uint64{},
			},
			originalData: &assets.Assets{
				OperInfo: []*assets.PersonBase{
					{Name: "新运维", Id: "1"},
				},
				OperSource: map[string]string{
					"0": "新运维",
				},
			},
			wantErr: false,
		},
		{
			name: "更新机房",
			mergeResult: &assets.Assets{
				MachineRoom:       []string{"旧机房"},
				MachineRoomSource: map[string]string{},
				AllSourceIds:      []uint64{},
				SourceIds:         []uint64{},
			},
			originalData: &assets.Assets{
				MachineRoom: []string{"新机房"},
				MachineRoomSource: map[string]string{
					"0": "新机房",
				},
			},
			wantErr: false,
		},
		{
			name: "更新型号",
			mergeResult: &assets.Assets{
				Model:        []string{"旧型号"},
				ModelSource:  map[string]string{},
				AllSourceIds: []uint64{},
				SourceIds:    []uint64{},
			},
			originalData: &assets.Assets{
				Model: []string{"新型号"},
				ModelSource: map[string]string{
					"0": "新型号",
				},
			},
			wantErr: false,
		},
		{
			name: "更新制造商",
			mergeResult: &assets.Assets{
				Maker:        []string{"旧制造商"},
				MakerSource:  map[string]string{},
				AllSourceIds: []uint64{},
				SourceIds:    []uint64{},
			},
			originalData: &assets.Assets{
				Maker: []string{"新制造商"},
				MakerSource: map[string]string{
					"0": "新制造商",
				},
			},
			wantErr: false,
		},
		{
			name: "更新标签",
			mergeResult: &assets.Assets{
				Tags:         []string{"旧标签"},
				TagsSource:   map[string]string{},
				AllSourceIds: []uint64{},
				SourceIds:    []uint64{},
			},
			originalData: &assets.Assets{
				Tags: []string{"新标签1", "新标签2"},
				TagsSource: map[string]string{
					"0": "新标签1,新标签2",
				},
			},
			wantErr: false,
		},
		{
			name: "更新所有字段",
			mergeResult: &assets.Assets{
				Business: []*assets.Business{
					{System: "旧系统", Owner: "旧负责人"},
				},
				OperInfo: []*assets.PersonBase{
					{Name: "旧运维", Id: "1"},
				},
				MachineRoom:       []string{"旧机房"},
				Model:             []string{"旧型号"},
				Maker:             []string{"旧制造商"},
				Tags:              []string{"旧标签"},
				BusinessSource:    map[string][]*assets.Business{},
				OperSource:        map[string]string{},
				MachineRoomSource: map[string]string{},
				ModelSource:       map[string]string{},
				MakerSource:       map[string]string{},
				TagsSource:        map[string]string{},
				AllSourceIds:      []uint64{},
				SourceIds:         []uint64{},
			},
			originalData: &assets.Assets{
				Business: []*assets.Business{
					{System: "新系统", Owner: "新负责人"},
				},
				OperInfo: []*assets.PersonBase{
					{Name: "新运维", Id: "2"},
				},
				MachineRoom: []string{"新机房"},
				Model:       []string{"新型号"},
				Maker:       []string{"新制造商"},
				Tags:        []string{"新标签1", "新标签2"},
				BusinessSource: map[string][]*assets.Business{
					"0": {
						{System: "新系统", Owner: "新负责人"},
					},
				},
				OperSource: map[string]string{
					"0": "新运维",
				},
				MachineRoomSource: map[string]string{
					"0": "新机房",
				},
				ModelSource: map[string]string{
					"0": "新型号",
				},
				MakerSource: map[string]string{
					"0": "新制造商",
				},
				TagsSource: map[string]string{
					"0": "新标签1,新标签2",
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &AssetManualCalibration{}
			err := m.UpdateMergeResult(tt.mergeResult, tt.originalData)

			if tt.wantErr {
				assert.Error(t, err)
				return
			}

			assert.NoError(t, err)

			// 验证业务系统更新
			if len(tt.originalData.BusinessSource) > 0 {
				assert.Equal(t, tt.originalData.Business, tt.mergeResult.Business)
				assert.Equal(t, tt.originalData.BusinessSource["0"], tt.mergeResult.BusinessSource["0"])
				assert.Contains(t, tt.mergeResult.AllSourceIds, uint64(0))
				assert.Contains(t, tt.mergeResult.SourceIds, uint64(0))
			}

			// 验证运维人员更新
			if len(tt.originalData.OperSource) > 0 {
				assert.Equal(t, tt.originalData.OperInfo, tt.mergeResult.OperInfo)
				assert.Equal(t, tt.originalData.OperSource["0"], tt.mergeResult.OperSource["0"])
				assert.Contains(t, tt.mergeResult.AllSourceIds, uint64(0))
				assert.Contains(t, tt.mergeResult.SourceIds, uint64(0))
			}

			// 验证机房更新
			if len(tt.originalData.MachineRoomSource) > 0 {
				assert.Equal(t, tt.originalData.MachineRoom, tt.mergeResult.MachineRoom)
				assert.Equal(t, tt.originalData.MachineRoomSource["0"], tt.mergeResult.MachineRoomSource["0"])
				assert.Contains(t, tt.mergeResult.AllSourceIds, uint64(0))
				assert.Contains(t, tt.mergeResult.SourceIds, uint64(0))
			}

			// 验证型号更新
			if len(tt.originalData.ModelSource) > 0 {
				assert.Equal(t, tt.originalData.Model, tt.mergeResult.Model)
				assert.Equal(t, tt.originalData.ModelSource["0"], tt.mergeResult.ModelSource["0"])
				assert.Contains(t, tt.mergeResult.AllSourceIds, uint64(0))
				assert.Contains(t, tt.mergeResult.SourceIds, uint64(0))
			}

			// 验证制造商更新
			if len(tt.originalData.MakerSource) > 0 {
				assert.Equal(t, tt.originalData.Maker, tt.mergeResult.Maker)
				assert.Equal(t, tt.originalData.MakerSource["0"], tt.mergeResult.MakerSource["0"])
				assert.Contains(t, tt.mergeResult.AllSourceIds, uint64(0))
				assert.Contains(t, tt.mergeResult.SourceIds, uint64(0))
			}

			// 验证标签更新
			if len(tt.originalData.TagsSource) > 0 {
				assert.Equal(t, tt.originalData.Tags, tt.mergeResult.Tags)
				assert.Equal(t, tt.originalData.TagsSource["0"], tt.mergeResult.TagsSource["0"])
				assert.Contains(t, tt.mergeResult.AllSourceIds, uint64(0))
				assert.Contains(t, tt.mergeResult.SourceIds, uint64(0))
			}
		})
	}
}

func TestGetDepartment(t *testing.T) {
	// 测试空部门列表
	emptyDepartments := []*personnel_departments.PersonnelDepartments{}
	result := getDeparment(emptyDepartments, 1, "test", "1", "", "")
	if result != nil {
		t.Errorf("Expected nil for empty departments list, got %v", result)
	}

	// 测试存在匹配部门的情况
	department1 := &personnel_departments.PersonnelDepartments{
		BaseModel: mysql.BaseModel{
			Id: 1,
		},
		FullName: "公司",
		ParentId: 0,
	}
	departments := []*personnel_departments.PersonnelDepartments{department1}
	result = getDeparment(departments, 1, "test", "1", "", "")
	if result == nil {
		t.Errorf("Expected department, got nil")
	} else if result.Id != 1 || result.Name != "公司" {
		t.Errorf("Expected department with Ids 1 and Name '公司', got %v", result)
	}

	// 测试存在子部门的情况
	department2 := &personnel_departments.PersonnelDepartments{
		BaseModel: mysql.BaseModel{
			Id: 2,
		},
		FullName: "公司/行政中心",
		ParentId: 1,
	}
	departments = append(departments, department2)
	result = getDeparment(departments, 2, "test", "1", "", "")
	if result == nil {
		t.Errorf("Expected department, got nil")
	} else if result.Id != 2 || result.Name != "公司/行政中心" {
		t.Errorf("Expected department with Ids 2 and Name '公司/行政中心', got %v", result)
	} else if len(result.Parents) != 1 {
		t.Errorf("Expected 1 parent department, got %d", len(result.Parents))
	} else if result.Parents[0].Id != 1 || result.Parents[0].Name != "公司" {
		t.Errorf("Expected parent department with Ids 1 and Name '公司', got %v", result.Parents[0])
	}
}

func TestGetAssetAllowKeys(t *testing.T) {
	// 配置化改造后，字段列表从配置映射动态生成
	expectedKeys := []string{
		"business",
		"oper",
		"machine_room",
		"model",
		"maker",
		"tag",
		"custom_fields",
		"rule_infos", // 新增字段
	}

	keys := GetAssetAllowKeys()
	if len(keys) != len(expectedKeys) {
		t.Errorf("Expected %d keys, got %d", len(expectedKeys), len(keys))
	}

	// 验证所有期望的字段都存在（不要求顺序）
	keyMap := make(map[string]bool)
	for _, key := range keys {
		keyMap[key] = true
	}

	for _, expectedKey := range expectedKeys {
		if !keyMap[expectedKey] {
			t.Errorf("Expected key %s not found in result", expectedKey)
		}
	}
}

func TestGetDisplayName(t *testing.T) {
	m := &AssetManualCalibration{}

	testCases := map[string]string{
		"business":      "业务系统",
		"oper":          "运维人员",
		"machine_room":  "机房",
		"model":         "型号",
		"maker":         "制造商",
		"tag":           "标签",
		"custom_fields": "自定义字段",
		"rule_infos":    "规则信息", // 新增字段
		"unknown":       "",        // 不存在的字段应返回空字符串
	}

	for key, expectedDisplayName := range testCases {
		displayName := m.GetDisplayName(key)
		if displayName != expectedDisplayName {
			t.Errorf("For key %s, expected display name %s, got %s", key, expectedDisplayName, displayName)
		}
	}
}

// TestConfigDriven_FieldConfig 测试配置驱动的字段配置
func TestConfigDriven_FieldConfig(t *testing.T) {
	// 测试字段配置映射
	t.Run("字段配置映射", func(t *testing.T) {
		// 验证配置映射包含所有必要字段
		expectedFields := []string{"business", "oper", "machine_room", "model", "maker", "tag", "custom_fields", "rule_infos"}

		for _, field := range expectedFields {
			config, exists := assetFieldConfigs[field]
			assert.True(t, exists, "字段 %s 应该存在于配置中", field)
			assert.NotEmpty(t, config.DisplayName, "字段 %s 应该有显示名称", field)
			assert.NotEmpty(t, config.Key, "字段 %s 应该有key", field)
		}

		// 验证复杂字段标识正确
		assert.True(t, assetFieldConfigs["business"].IsComplex)
		assert.True(t, assetFieldConfigs["oper"].IsComplex)
		assert.True(t, assetFieldConfigs["rule_infos"].IsComplex)

		// 验证简单字段标识正确
		assert.False(t, assetFieldConfigs["machine_room"].IsComplex)
		assert.False(t, assetFieldConfigs["model"].IsComplex)
		assert.False(t, assetFieldConfigs["maker"].IsComplex)
		assert.False(t, assetFieldConfigs["tag"].IsComplex)
		assert.False(t, assetFieldConfigs["custom_fields"].IsComplex)
	})

	// 测试处理函数映射
	t.Run("处理函数映射", func(t *testing.T) {
		// 验证复杂字段都有对应的处理函数
		complexFields := []string{"business", "oper", "rule_infos"}

		for _, field := range complexFields {
			handler, exists := complexFieldHandlers[field]
			assert.True(t, exists, "复杂字段 %s 应该有处理函数", field)
			assert.NotNil(t, handler, "处理函数不应该为nil")
		}
	})
}

// TestAutoProcessSimpleField 测试简单字段自动处理函数
func TestAutoProcessSimpleField(t *testing.T) {
	testCases := []struct {
		name        string
		fieldName   string
		input       string
		expected    []string
		expectError bool
	}{
		{
			name:      "正常JSON数组",
			fieldName: "machine_room",
			input:     `["机房A", "机房B"]`,
			expected:  []string{"机房A", "机房B"},
		},
		{
			name:      "单个元素",
			fieldName: "model",
			input:     `["型号X"]`,
			expected:  []string{"型号X"},
		},
		{
			name:      "空数组",
			fieldName: "tag",
			input:     `[]`,
			expected:  []string{},
		},
		{
			name:        "无效JSON",
			fieldName:   "maker",
			input:       `["无效JSON`,
			expectError: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result, err := autoProcessSimpleField(tc.fieldName, tc.input)

			if tc.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "JSON解析失败")
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tc.expected, result)
			}
		})
	}
}

// TestFieldValidation 测试字段验证逻辑
func TestFieldValidation(t *testing.T) {
	// 测试支持的字段
	t.Run("支持的字段", func(t *testing.T) {
		supportedFields := []string{"business", "oper", "machine_room", "model", "maker", "tag", "custom_fields", "rule_infos"}

		for _, field := range supportedFields {
			_, exists := assetFieldConfigs[field]
			assert.True(t, exists, "字段 %s 应该被支持", field)
		}
	})

	// 测试不支持的字段
	t.Run("不支持的字段", func(t *testing.T) {
		unsupportedFields := []string{"unknown_field", "invalid_field", "test_field"}

		for _, field := range unsupportedFields {
			_, exists := assetFieldConfigs[field]
			assert.False(t, exists, "字段 %s 不应该被支持", field)
		}
	})
}
